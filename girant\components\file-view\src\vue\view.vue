<script setup lang="ts">
// 引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx';
// 引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel';
// pdf组件
import VueOfficePdf from '@vue-office/pdf';

const props = window.opener?.initData || {};
</script>

<template>
  <div>
    <VueOfficePdf :src="props.fileUrl" v-if="props.fileType === 'pdf'" />
    <VueOfficeDocx :src="props.fileUrl" v-else-if="props.fileType === 'docx'" />
    <VueOfficeExcel
      :src="props.fileUrl"
      v-else-if="props.fileType === 'xlsx'"
    />
    <div v-else-if="props.fileType === 'txt'">{{ props.resText }}</div>
    <span v-else>{{ props.text }}</span>
  </div>
</template>
