<script setup lang="ts">
// 引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx';
// 引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel';
// pdf组件
import VueOfficePdf from '@vue-office/pdf';

const props = defineProps({
  fileUrl: {
    default: '',
    type: String,
  },
  fileType: {
    default: '',
    type: String,
  },
  resText: {
    default: '',
    type: String,
  },
  text: {
    default: '',
    type: String,
  },
});
defineExpose({
  props,
});
// 计时器，每秒触发一次
// const timer = setInterval(() => {
//   console.log('timer', props.fileUrl);
// }, 1000);
</script>

<template>
  <div>
    <VueOfficePdf :src="fileUrl" v-if="fileType === 'pdf'" />
    <VueOfficeDocx :src="fileUrl" v-else-if="fileType === 'docx'" />
    <VueOfficeExcel :src="fileUrl" v-else-if="fileType === 'xlsx'" />
    <div v-else-if="fileType === 'txt'">{{ resText }}</div>
    <span v-else>{{ text }}</span>
  </div>
</template>
