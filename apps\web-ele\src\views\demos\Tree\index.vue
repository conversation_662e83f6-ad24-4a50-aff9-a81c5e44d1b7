<!-- eslint-disable no-console -->
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { SvgAvatar1Icon, SvgAvatar2Icon } from '@vben/icons';
import { cloneDeep } from '@vben/utils';

import { Tree } from '@girant-web/tree-component';
import { defaultRequestClient } from '@girant/utils';
import { ElButton, ElCard, ElMessage } from 'element-plus';

// 数据源
const dataSource = ref([
  {
    id: 1,
    label: '部门1 不指明',
    icon: 'icon-[mdi--folder]',
    children: [
      {
        id: 2,
        label: '部门1.1 不指明',
        children: [
          {
            id: 3,
            icon: 'icon-[mdi--folder]',
            label: '人1 指明',
            isLeaf: true,
          },
          { id: 4, label: '人2 指明', isLeaf: true },
        ],
      },
      {
        id: 5,
        label: '人3 不指明 ',
        checked: true,
      },
    ],
  },
  {
    id: 6,
    label: '部门2 指明',
    isLeaf: false,
  },
  {
    id: 7,
    label: '部门3 指明',
    isLeaf: false,
  },
]);
const dataSource2 = ref([
  {
    id: 1,
    label: '部门1',
    icon: 'icon-[mdi--folder]',
    type: 'dept',
    children: [
      {
        id: 5,
        label: '人1',
        checked: true,
        type: 'user',
      },
      {
        id: 8,
        label: '人3',
        checked: true,
        type: 'user',
      },
      {
        id: 9,
        label: '人2',
        checked: true,
        type: 'user',
      },
    ],
  },
  {
    id: 14,
    label: '部门2',
    type: 'dept',
  },
]);
const dataSource3 = ref([
  {
    id: 1,
    label: '部门1',
    icon: 'icon-[mdi--folder]',
    type: 'dept',
    isDisable: true,
  },
  {
    id: 14,
    label: '部门2',
    type: 'dept',
  },
]);
const dataSource4 = ref([
  {
    id: 1,
    label: '部门1 不指明',
    icon: 'icon-[mdi--folder]',
    children: [
      {
        id: 2,
        label: '部门1.1 不指明',
        children: [
          {
            id: 3,
            icon: 'icon-[mdi--folder]',
            label: '人1 指明',
            isLeaf: true,
          },
          { id: 4, label: '人2 指明', isLeaf: true },
        ],
      },
      {
        id: 5,
        label: '人3 不指明 ',
        checked: true,
      },
    ],
  },
  {
    id: 6,
    label: '部门2 指明',
    isLeaf: false,
  },
  {
    id: 7,
    label: '部门3 指明',
    isLeaf: false,
  },
]);
const treeRef = ref();
const treeRef2 = ref({
  searchText: '',
  searchFilter: (data: any) => {
    console.log('搜索', data);
  },
});
const fetchData = ref([]);
const getData = async () => {
  const data = await defaultRequestClient.post(
    '/base-data/v1/base/dept/getDeptTree',
  );
  fetchData.value = data;
};
// 查看树组件勾选
const showTree = () => {
  console.log('NodeList', treeRef.value.checkedNodeListNode);
  console.log('dataList', treeRef.value.checkedNodeListData);
  console.log('idList', treeRef.value.checkedNodeListId);
  console.log('HalfCheckedNodes', treeRef.value.tree.getHalfCheckedNodes());
  console.log('HalfCheckedKeys', treeRef.value.tree.getHalfCheckedKeys());
};
// 映射数据1 远程部门
const mapData = {
  label: 'deptName',
  children: 'children',
  id: 'deptId',
  isLeaf: (data: any) => data.children?.length === 0,
};
// 映射数据2
const mapData2 = {
  disabled: 'isDisable',
};
const mapData3 = {
  isLeaf: (data: any) => data.type === 'user',
};
// 映射数据4 菜单
const mapData4 = {
  label: 'menuName',
  id: 'menuId',
};

// 删除后回调
const afterRemove = (data: any) => {
  console.log('删除后', data);
};
// 删除前回调 阻止继续删除
const handleBeforeRemove = ({
  data,
  fn,
}: {
  data: any;
  fn: (res: boolean) => void;
}) => {
  console.log('删除前', data);
  fn(false);
};
// 数据变化后回调
const afterTreeChanges = (newTree: any) => {
  console.log('树发生变化', newTree);
};
// 编辑前回调
const handleBeforeEdit = ({
  data,
  fn,
}: {
  data: any;
  fn: (res: boolean) => void;
}) => {
  console.log('编辑前', data);
  fn(false);
};
// 编辑后回调
const afterEdit = (data: any) => {
  console.log('编辑后', data);
};
// 发送请求前回调
const beforeRequest = (data: any) => {
  console.log('发送请求前', data);
};
// 发送请求后回调
const afterRequest = (data: any) => {
  console.log('发送请求后', data);
};
// 部门1 不指明 节点
const aNode = computed(() => {
  return treeRef.value.treeAll.find(
    (item: any) => item.label === '部门1 不指明',
  );
});
// 节点点击前回调
const handleBeforeClick = (data: any) => {
  console.log('节点点击前', data);
};
// 获取节点点击 // 获取下一个兄弟节点
const getNodeClick = (data: any, node: any) => {
  console.log('当前节点', data, node);
  const nextSiblingNode = treeRef.value.getNextSiblingNode(node);
  const previousSiblingNode = treeRef.value.getPreviousSiblingNode(node);
  const nextNode = treeRef.value.getNextNode(node);
  const previousNode = treeRef.value.getPreviousNode(node);
  const parentNode = treeRef.value.getParentNode(node);
  console.log('上一个兄弟节点', previousSiblingNode);
  console.log('下一个兄弟节点', nextSiblingNode);
  console.log('下一个节点', nextNode);
  console.log('上一个节点', previousNode);
  console.log('父节点', parentNode);
};
async function checkNode(node: any, checked: any) {
  // 当前选中节点的node
  const currentNode = treeRef.value.tree.getNode(node);
  // 获取所有子节点
  const a = treeRef.value.getAllChildren(node);
  // 获取所有选中节点的多级父节点（包含根节点，去重）
  const b = treeRef.value.getAllLevelParents(currentNode);
  console.log('当前选中节点的node', node, checked);
  console.log('获取所有子节点', a);
  console.log('多级父节点', b);
}
// 切换父子关联
const handleStrictly = () => {
  treeRef.value.useConfig.checkStrictly =
    !treeRef.value.useConfig.checkStrictly;
};
// 测试控制
const show = ref(false);
onMounted(() => {
  getData();
});

// 自定义过滤函数
const filterFn = (value: any, data: any) => {
  if (!value) {
    data.class = '';
    return true;
  }
  if (data.label.includes(value)) {
    ElMessage.info(`找到了：${data.label}`);
    data.class = 'bg-amber-400';
    return true;
  } else {
    data.class = '';
    return false;
  }
};
// 搜索值
const searchText = ref('');
</script>
<template>
  <Page description="树组件使用测试" title="树组件使用测试">
    <ElCard
      header="  使用外部数据 单选模式 查看勾选 全选 取消全选 节点展开 节点收起 控制台查看 点击前回调 
      点击获取节点 上一个兄弟节点 下一个兄弟节点 下一个节点 上一个节点 父节点"
    >
      <ElButton @click="showTree">查看勾选</ElButton>
      <ElButton @click="treeRef.disabledAll(true)">禁用全部</ElButton>
      <ElButton @click="treeRef.disabledAll(false)">启用全部</ElButton>
      <ElButton @click="treeRef.checkAllNodes(true)">全选</ElButton>
      <ElButton @click="treeRef.checkAllNodes(false)">取消全选</ElButton>
      <ElButton @click="treeRef.expandNode(aNode)"> 展开部门1节点 </ElButton>
      <ElButton @click="treeRef.collapseNode(aNode)"> 收起部门1节点 </ElButton>
      <ElButton @click="treeRef.expandAllNode()"> 展开所有一级节点 </ElButton>
      <ElButton @click="treeRef.collapseAllNode()"> 收起所有一级节点 </ElButton>
      <ElButton @click="handleStrictly">
        切换父子关联{{ treeRef?.useConfig.checkStrictly }}
      </ElButton>
      <ElButton @click="show = !show"> 切换图标显示 </ElButton>
      <Tree
        ref="treeRef"
        :multiple-choice="true"
        :show-icon="show"
        :show-checkbox="true"
        :data-source="cloneDeep(dataSource)"
        @node-click="getNodeClick"
        @before-click="handleBeforeClick"
        highlight-current
        :draggable="false"
        @check="checkNode"
      />
    </ElCard>
    <ElCard header="上下级同时勾选">
      <Tree
        :multiple-choice="true"
        :show-checkbox="true"
        :check-strictly="true"
        :data-source="cloneDeep(dataSource)"
        :check-all-parent-and-children="true"
      />
    </ElCard>
    <ElCard header="单选模式">
      <Tree
        :multiple-choice="false"
        :show-checkbox="true"
        :data-source="cloneDeep(dataSource)"
      />
    </ElCard>

    <ElCard header="设置文本最大长度 50px">
      <Tree
        :show-checkbox="true"
        :data-source="cloneDeep(dataSource)"
        max-text-length="50px"
      />
    </ElCard>

    <ElCard header="父子不互相关联">
      <Tree
        :show-checkbox="true"
        :check-strictly="true"
        :data-source="cloneDeep(dataSource)"
      />
    </ElCard>

    <ElCard header="根据data中的数据禁用复选框">
      <Tree
        :show-checkbox="true"
        :data-source="dataSource3"
        :map-data="mapData2"
      />
    </ElCard>

    <ElCard header="禁用所有复选框">
      <Tree
        :show-checkbox="true"
        :disabled="true"
        :data-source="cloneDeep(dataSource)"
      />
    </ElCard>

    <ElCard
      header="通过传入isLeaf节点判断函数来确切是否是子节点 isLeaf: (data: any) => data.type === 'user'"
    >
      <Tree
        :data-source="cloneDeep(dataSource2)"
        :map-data="mapData3"
        :show-icon="true"
      />
    </ElCard>

    <ElCard header="通过插槽自定义节点图标和文字">
      <Tree :data-source="cloneDeep(dataSource2)">
        <template #icon="{ data }">
          <SvgAvatar1Icon v-if="data.type === 'dept'" />
          <SvgAvatar2Icon v-else />
        </template>
        <template #text="{ data }">
          <span
            :class="data.type === 'dept' ? 'text-fuchsia-500' : 'text-black'"
            >{{ data.label }}
          </span>
        </template>
      </Tree>
    </ElCard>

    <ElCard
      header="Svg 和 Tailwind CSS图标 设置父节点、叶子节点图标 通过data数据指明是否是叶子节点"
    >
      <Tree
        :data-source="cloneDeep(dataSource)"
        :show-icon="true"
        :parent-icon="SvgAvatar1Icon"
        leaf-icon="icon-[line-md--file]"
      />
    </ElCard>

    <ElCard header="启用搜索框 高亮显示 不展开节点">
      <Tree
        :show-search="true"
        :data-source="cloneDeep(dataSource2)"
        :default-expand-all="false"
      />
    </ElCard>

    <ElCard header="启用搜索框 过滤 ">
      <Tree
        :show-search="true"
        :search-mode="2"
        :data-source="cloneDeep(dataSource2)"
      />
    </ElCard>
    <ElCard header="启用搜索框 自定义过滤">
      <Tree
        :show-search="true"
        highlight-class="bg-red-500"
        :data-source="cloneDeep(dataSource2)"
        :default-expand-all="false"
        :filter-node-method="filterFn"
      />
    </ElCard>
    <ElCard
      header="使用搜索按钮 使用插槽搜索  高亮显示 自定义背景色 bg-red-100"
    >
      <ElInput
        placeholder="外部搜索"
        v-model="treeRef2.searchText"
        class="!w-[200px]"
      />
      <ElInput
        placeholder="点击按钮搜索"
        v-model="searchText"
        class="!w-[200px]"
      /><ElButton type="primary" @click="treeRef2.searchFilter(searchText)">
        搜索
      </ElButton>
      <Tree
        :show-search="true"
        highlight-class="bg-red-100"
        :data-source="dataSource4"
        ref="treeRef2"
        :show-icon="true"
      >
        <template #search="{ searchText }">
          {{ searchText }}
          <ElInput
            placeholder="插槽搜索"
            v-model="treeRef2.searchText"
            class="!w-[200px]"
          />
        </template>
      </Tree>
    </ElCard>
    <ElCard
      header="开启编辑 开启删除 监听删除后 监听树发生变化后 监听编辑后变化"
    >
      <Tree
        :show-edit="true"
        :show-remove="true"
        @after-remove="afterRemove"
        :data-source="cloneDeep(dataSource)"
        :show-icon="true"
        @after-tree-changes="afterTreeChanges"
        @after-edit="afterEdit"
      >
        <template #empty>
          <div>自定义空数据提示</div>
        </template>
      </Tree>
    </ElCard>

    <ElCard
      header="开启编辑 开启删除 使用编辑区域operate插槽 监听删除前 并阻止删除 监听编辑前 并阻止编辑"
    >
      <Tree
        :show-edit="true"
        :show-remove="true"
        :data-source="cloneDeep(dataSource)"
        :show-checkbox="true"
        @before-remove="handleBeforeRemove"
        @before-edit="handleBeforeEdit"
      >
        <template #operate="{ node, data, removeNode, editNode }">
          <ElButton
            type="primary"
            link
            @click="
              () => {
                console.log(node, data);
                editNode(data);
              }
            "
          >
            自定义编辑按钮
          </ElButton>
          <ElButton
            type="primary"
            link
            @click="
              () => {
                removeNode(data);
              }
            "
          >
            自定义删除按钮
          </ElButton>
        </template>
      </Tree>
    </ElCard>

    <ElCard header="开启懒加载">
      <Tree
        :lazy="true"
        :show-checkbox="true"
        :data-source="cloneDeep(dataSource)"
      />
    </ElCard>

    <ElCard header="开启懒加载  关闭多选">
      <Tree
        :show-checkbox="true"
        :show-search="true"
        :lazy="true"
        :data-source="fetchData"
        :map-data="mapData"
        :multiple-choice="false"
      />
    </ElCard>

    <ElCard header="开启懒加载 模拟加载失败 控制台提示请求失败回调">
      <Tree
        :lazy="true"
        :get-tree-api="{ url: '/base-data/v1/base/dept/getDeptTre' }"
        :map-data="mapData"
        @request-failed="
          () => {
            console.log('请求失败回调');
          }
        "
      />
    </ElCard>

    <ElCard
      header="使用查询部门树接口/base-data/view/v1/base/dept/getDeptTree 监听请求前,请求后"
    >
      <Tree
        :get-tree-api="{ url: '/base-data/v1/base/dept/getDeptTree' }"
        :map-data="mapData"
        :show-icon="true"
        @before-request="beforeRequest"
        @after-request="afterRequest"
      />
    </ElCard>
    <ElCard header="使用获取当前用户菜单接口/system/v1/perms/mi/getMenus">
      <Tree
        :get-tree-api="{
          url: '/system/v1/perms/mi/getMenus',
          method: 'get',
        }"
        :map-data="mapData4"
        :show-icon="true"
        :show-checkbox="true"
      />
    </ElCard>
  </Page>
</template>
