<!-- eslint-disable no-console -->
<script setup lang="ts">
/**
 * @demos UploadFiles
 * @description 文件上传组件测试
 * <AUTHOR>
 * @date [2025-7-23]
 */

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { UploadFiles } from '@girant-web/upload-files-component';
import { ElCard } from 'element-plus';

const editRef = ref();
const getCompleteStatus = async () => {
  const isCompleted = await editRef.value.getCompleteStatus();
  console.log('上传完成情况', isCompleted);
};
const getFileListByStatus = async () => {
  const all = await editRef.value.getFileListByStatus();
  const success = await editRef.value.getFileListByStatus('success');
  const fail = await editRef.value.getFileListByStatus('fail');
  const ready = await editRef.value.getFileListByStatus('ready');
  const uploading = await editRef.value.getFileListByStatus('uploading');
  console.log('获取全部文件列表', all);
  console.log('获取上传成功的文件列表', success);
  console.log('获取上传失败的文件列表', fail);
  console.log('获取等待上传的文件列表', ready);
  console.log('获取上传中的文件列表', uploading);
};
const getFileCountByStatus = async () => {
  const all = await editRef.value.getFileCountByStatus();
  const success = await editRef.value.getFileCountByStatus('success');
  const fail = await editRef.value.getFileCountByStatus('fail');
  const ready = await editRef.value.getFileCountByStatus('ready');
  const uploading = await editRef.value.getFileCountByStatus('uploading');
  console.log('获取全部文件数量', all);
  console.log('获取上传成功的文件数量', success);
  console.log('获取上传失败的文件数量', fail);
  console.log('获取等待上传的文件数量', ready);
  console.log('获取上传中的文件数量', uploading);
};
const getSerialNumber = () => {
  const serialNumber = editRef.value.getSerialNumber();
  console.log('获取上传使用的流水号', serialNumber);
};
const handleSuccess = (response: any) => {
  console.log('上传成功', response);
};
const handleDelete = (file: any) => {
  console.log('删除文件', file);
};
const handleListDelete = () => {
  console.log('清空文件列表');
};
</script>
<template>
  <Page description="上传组件使用测试" title="上传组件使用测试">
    <ElCard header="编辑模式">
      <UploadFiles mode="editMode" :limit="10" :auto-upload="false" />
    </ElCard>
    <ElCard header="只读模式 设置高度 !h-[200px]">
      <UploadFiles
        mode="readMode"
        serial-number="579831102911284224"
        :table-props="{ class: '!h-[200px]' }"
      />
    </ElCard>
    <ElCard header="编辑模式 设置宽度">
      <UploadFiles
        mode="editMode"
        :limit="10"
        :auto-upload="false"
        serial-number="602681945704367104"
        class="w-[400px]"
      />
    </ElCard>
    <ElCard header="编辑模式">
      <UploadFiles
        ref="editRef"
        mode="editMode"
        :limit="10"
        :auto-upload="false"
        @file-submit-success="handleSuccess"
        @file-delete-success="handleDelete"
        @file-list-delete-success="handleListDelete"
      >
        <template #operateRegion>
          <div class="mb-3 mt-3">
            <ElButton type="success" @click="getCompleteStatus">
              获取上传完成情况
            </ElButton>
            <ElButton type="success" @click="getFileListByStatus">
              获取文件状态
            </ElButton>
            <ElButton type="success" @click="getFileCountByStatus">
              获取文件数量
            </ElButton>
            <ElButton type="success" @click="getSerialNumber">
              获取流水号
            </ElButton>
            <ElButton type="success" @click="editRef.uploadAllFiles()">
              上传全部文件
            </ElButton>
            <ElButton type="success" @click="editRef.clearFileList()">
              清空文件列表
            </ElButton>
          </div>
        </template>
      </UploadFiles>
    </ElCard>
    <ElCard header="编辑模式 文件类型限制xls">
      <UploadFiles
        mode="editMode"
        :limit="10"
        :auto-upload="false"
        :allowed-formats="['xls']"
      />
    </ElCard>
  </Page>
</template>
