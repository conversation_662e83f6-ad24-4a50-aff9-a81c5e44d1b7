# 上传文件组件 UploadFiles

使用了

使用了私有仓库的：

1.@girant-web/file-view-component

2.@girant-web/img-view-component

3.@girant/utils

使用了 compressorjs 压缩库

使用了vben的:

1.element-plus

2.@vben/common-ui

3.@vben/request

4.@vben/utils

导入使用import { UploadFiles } from '@girant-web/upload-files-component';

本组件由三部分组成，从上到下依次是：ElUpload组件、operateRegion插槽、table插槽（现内容是ElTable）

组件的<slot></slot>放在ElTable中

element-plus Table表格的 Table属性可用，Table事件可用，插槽可用，用法和element-plus一致

### Props

| 属性名 | 类型 | props默认值 | editMode模式默认值 | readMode模式默认值 | 说明 |
| :-- | :-: | :-- | :-: | :-: | :-- |
| mode | String | 'editMode' | - | - | 选择模式传入的值为(editMode、readMode) |
| drag | Boolean | null | true | false | 是否支持拖拽上传 |
| multiple | Boolean | null | true | false | 是否可以多选 |
| autoUpload | Boolean | true | - | - | 是否自动上传 |
| showOperateRegion | Boolean | true | - | readMode模式下不显示 | 是否显示操作区域，包含‘上传文件、清空文件’按钮（设置false后showClearFileBtn，showUploadFileBtn都不会显示） |
| showClearFileBtn | Boolean | true | - | readMode模式下不显示 | 是否显示清空文件按钮 |
| showUploadFileBtn | Boolean | true | - | readMode模式下不显示 | 是否显示上传文件按钮（在自动上传下不显示） |
| deleteUploadedFilesOnClear | Boolean | null | true | false | 在清空时是否同时删除已上传文件 |
| showDeleteBtn | Boolean | null | true | false | 控制Table操作列中的删除按钮是否显示 |
| showDownloadBtn | Boolean | null | true | true | 控制Table操作列中的下载按钮是否显示（下载按钮在文件上传完成后才会出现） |
| showFileList | Boolean | true | - | - | 是否显示Table文件列表 |
| showThumbnail | Boolean | false | - | - | 是否显示缩略图模板 {file插槽} |
| compressImg | Number | 0 | - | - | 压缩图片质量 0-1(支持一位小数，例如输入0.8表示压缩到原来的80%) (0为不开启压缩)只支持图片 |
| fileSize | Number | 600 | - | - | 文件大小限制(0为不限制,单位MB)后端限制最多600MB |
| limit | Number | 20 | - | - | 限制上传文件数量(0为不限制) |
| serialNumber | String | null | - | - | 传入的流水号19位（如果没有传入，则由组件生成） |
| loadFilesBySerialNumber | Boolean | true | - | - | 是否根据流水号请求获取文件列表 |
| tableColumn | object | null | editTableColumn | readTableColumn | 上传文件列表列显示配置，传入的值见下表tableColumnType |
| allowedFormats | Array | null | - | - | 允许上传的文件(字符串数组，空为不限制)使用文件扩展名，常见文件扩展名见下表补充 |
| fileThumbnailSize | Object | 50 | - | - | 文件预览缩略图大小，单位px,宽高相同，图片缩放fit使用cover策略 |
| textConfig | Object | textConfig | - | - | 文本配置。类型说明见下表textConfigType。默认值见下表textConfig |
| urlConfig | Object | urlConfig | - | - | 文件请求路径配置。见下表urlConfig。默认值见下表urlConfig |
| uploadProps | Object | null | - | - | 要绑定到ElUpload上的Props属性，参考ElUpload |
| tableProps | Object | null | - | - | 要绑定到ElUpload上的Props属性，参考ElTable |

### Slots

| 插槽名 | 参考 | 说明 |
| --- | --- | --- |
| trigger | ElUpload的trigger | 触发文件选择框的内容（选择区域） |
| uploadDefault | ElUpload的default | 自定义默认内容 （trigger下面区域） |
| tip | ElUpload的tip | 提示说明文字（uploadDefault下面区域） |
| file | ElUpload的file | 文件缩略图插槽（tip下面区域）。showThumbnail为true才有效 |
| operateRegion |  | 列表左上角操作区域，目前包含 上传文件、清空文件按钮 |
| table |  | 表格区域 目前包含了整个ElTable。 showFileList为true才有效 |
| append | ElTable的append | 插入至表格最后一行之后的内容（表格结尾 ） |
| empty | ElTable的empty | 当列表数据为空时自定义的内容 |

### Exposes

| 对象名 | 类型 | 说明 |
| --- | --- | --- |
| getFileListByStatus | (status：fileUploadStatus)=>FileList | 获取文件列表，根据传入的文件状态获取文件，默认不传则获取全部文件。fileUploadStatus见下表类型说明 |
| getCompleteStatus | ()=>Boolean | 获取上传完成情况，全部上传完成返回true,否则返回false。 |
| getFileCountByStatus | (status：fileUploadStatus)=>Number | 获取文件数量，传入状态获取对应文件数量，默认不传则获取全部数量。fileUploadStatus见下表类型说明 |
| uploadAllFiles | ()=>void | 上传全部文件（已上传的不会再次上传，上传失败的会重新上传） |
| clearFileList | () => Promise<void> | 清空文件列表 |
| getSerialNumber | ()=>string | 获取上传使用的流水号 |

### Event

| 事件 | 类型 | 说明 |
| --- | --- | --- |
| fileSubmitSuccess | (response)=>void | 上传完成后 返回上传结果信息（每一个文件上传成功后都会触发），response的值是后端接口返回的值 |
| fileListDeleteSuccess | ()=>void | 清空文件完成后触发 |
| fileDeleteSuccess | (file)=>void | 删除单个文件完成后触发，file是文件详细信息 |

### 类型说明

```ts
//label是列的标头名，props是这个列的绑定配置，具体配置参考 Table-column API
//https://element-plus.org/zh-CN/component/table.html#table-column-api
interface tableColumnItem {
  label: string;
  props: Record<string, any>;
}
//上传列表显示控制 根据label值来控制列表头的名称。如果不传入某个属性，表示不渲染这一行，例如：
//tableColumnType = {fileName: {label:'自定义文件名'}};表示只显示 ‘自定义文件名’这一行 全部属性如下
interface tableColumnType {
  fileName: tableColumnItem; //文件名
  fileOperate: tableColumnItem; //操作
  filePreview: tableColumnItem; //文件预览
  fileProgress: tableColumnItem; //上传进度
  fileSize: tableColumnItem; //文件大小
  fileStatus: tableColumnItem; //文件状态
}

//文本配置类型
interface textConfigType {
  selectFileBtnText: string; //选择文件按钮文字
  clearBtnText: string; //清空按钮文字
  submitBtnText: string; //提交按钮文字
  uploadPlaceholder: string; //上传区域提示文字
  formatText: string; //文件格式支持提示
  dropUploadText: string; //上传区域提示文字 开启拖拽
}
//文件请求路径类型
interface urlConfigType {
  uploadFileUrl: string; // 文件上传接口地址
  deleteFileUrl: string; // 文件删除接口地址
  downloadFileUrl: string; // 文件下载接口地址
  getFileDetailUrl: string; // 根据文件id获取文件详情接口地址
  getFileListUrl: string; // 流水号获取文件信息列表接口地址
}
//fail上传失败，ready 等待上传，success 上传成功，uploading，上传中
type fileUploadStatus = 'fail' | 'ready' | 'success' | 'uploading';
```

### 组件默认值说明

```TS
//编辑模式列配置
const editTableColumn={
  fileName: { label: '文件名', props: { minWidth: '100px' } },
  fileOperate: { label: '操作', props: { minWidth: '140px' } },
  filePreview: { label: '文件预览', props: { minWidth: '100px' } },
  fileProgress: { label: '上传进度', props: { minWidth: '140px' } },
  fileStatus: { label: '文件状态', props: { minWidth: '100px' } },
  fileSize: { label: '文件大小', props: { minWidth: '100px' } },
}
//只读模式列配置
const readTableColumn={
  fileName: { label: '文件名', props: { minWidth: '100px' } },
  fileOperate: { label: '操作', props: { minWidth: '140px' } },
  filePreview: { label: '文件预览', props: { minWidth: '100px' } },
}
//文本配置
const textConfig={
  selectFileBtnText: '选择文件',//选择文件按钮文字
  clearBtnText: '清空文件', 	//清空按钮文字
  submitBtnText: '上传文件',	//提交按钮文字
  uploadPlaceholder: '点击上传',//上传区域提示文字
  dropUploadText:'将文件拖到此处，或点击上传' //上传区域提示文字 开启拖拽
  formatText: '支持扩展名:rar .zip .doc .docx .pdf .jpg...', 	//文件格式支持提示
}
//请求文件地址配置
const urlConfig={
    uploadFileUrl:'/file-manage/view/v1/file/upload',		// 文件上传接口地址
    deleteFileUrl:'/file-manage/view/v1/file/remove', 		// 文件删除接口地址
    downloadFileUrl:'/file-manage/view/v1/file/download';   // 文件下载接口地址
  	getFileDetailUrl:'/file-manage/view/v1/file/getFile';   // 根据文件id获取文件详情接口地址
  	getFileListUrl: '/file-manage/view/v1/file/getFileList';// 流水号获取文件信息列表接口地址
}
```

### 使用例子

#### 1.正常使用

```vue
<!-- eslint-disable no-console -->
<script setup lang="ts">
/**
 * @demos UploadFiles
 * @description 文件上传组件测试
 * <AUTHOR>
 * @date [2025-7-23]
 */

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { UploadFiles } from '@girant-web/upload-files-component';
import { ElCard } from 'element-plus';

const editRef = ref();
const getCompleteStatus = async () => {
  const isCompleted = await editRef.value.getCompleteStatus();
  console.log('上传完成情况', isCompleted);
};
const getFileListByStatus = async () => {
  const all = await editRef.value.getFileListByStatus();
  const success = await editRef.value.getFileListByStatus('success');
  const fail = await editRef.value.getFileListByStatus('fail');
  const ready = await editRef.value.getFileListByStatus('ready');
  const uploading = await editRef.value.getFileListByStatus('uploading');
  console.log('获取全部文件列表', all);
  console.log('获取上传成功的文件列表', success);
  console.log('获取上传失败的文件列表', fail);
  console.log('获取等待上传的文件列表', ready);
  console.log('获取上传中的文件列表', uploading);
};
const getFileCountByStatus = async () => {
  const all = await editRef.value.getFileCountByStatus();
  const success = await editRef.value.getFileCountByStatus('success');
  const fail = await editRef.value.getFileCountByStatus('fail');
  const ready = await editRef.value.getFileCountByStatus('ready');
  const uploading = await editRef.value.getFileCountByStatus('uploading');
  console.log('获取全部文件数量', all);
  console.log('获取上传成功的文件数量', success);
  console.log('获取上传失败的文件数量', fail);
  console.log('获取等待上传的文件数量', ready);
  console.log('获取上传中的文件数量', uploading);
};
const getSerialNumber = () => {
  const serialNumber = editRef.value.getSerialNumber();
  console.log('获取上传使用的流水号', serialNumber);
};
const handleSuccess = (response: any) => {
  console.log('上传成功', response);
};
const handleDelete = (file: any) => {
  console.log('删除文件', file);
};
const handleListDelete = () => {
  console.log('清空文件列表');
};
</script>
<template>
  <Page description="上传组件使用测试" title="上传组件使用测试">
    <ElCard header="编辑模式">
      <UploadFiles mode="editMode" :limit="10" :auto-upload="false" />
    </ElCard>
    <ElCard header="只读模式 设置高度 !h-[200px]">
      <UploadFiles
        mode="readMode"
        serial-number="579831102911284224"
        :table-props="{ class: '!h-[200px]' }"
      />
    </ElCard>
    <ElCard header="编辑模式 设置宽度">
      <UploadFiles
        mode="editMode"
        :limit="10"
        :auto-upload="false"
        serial-number="602681945704367104"
        class="w-[400px]"
      />
    </ElCard>
    <ElCard header="编辑模式">
      <UploadFiles
        ref="editRef"
        mode="editMode"
        :limit="10"
        :auto-upload="false"
        @file-submit-success="handleSuccess"
        @file-delete-success="handleDelete"
        @file-list-delete-success="handleListDelete"
      >
        <template #operateRegion>
          <div class="mb-3 mt-3">
            <ElButton type="success" @click="getCompleteStatus">
              获取上传完成情况
            </ElButton>
            <ElButton type="success" @click="getFileListByStatus">
              获取文件状态
            </ElButton>
            <ElButton type="success" @click="getFileCountByStatus">
              获取文件数量
            </ElButton>
            <ElButton type="success" @click="getSerialNumber">
              获取流水号
            </ElButton>
            <ElButton type="success" @click="editRef.uploadAllFiles()">
              上传全部文件
            </ElButton>
            <ElButton type="success" @click="editRef.clearFileList()">
              清空文件列表
            </ElButton>
          </div>
        </template>
      </UploadFiles>
    </ElCard>
  </Page>
</template>
```

#### 2.在useVbenForm中使用

```ts
 //编辑回显
{
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
    }
//查看回显
    {
      component: h(UploadFiles, {
        mode: 'readMode',
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
    },

 //编辑和查看同时使用
     {
      component: h(UploadFiles, {
        mode: isViewMode ? 'readMode' : 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
    },
```

### 3.检查是否上传完成

```ts
//获取表单组件实例 调用getCompleteStatus获取上传完成结果 全部上传完成为true,反之为false
const serialNumber: any = await formApi?.getFieldComponentRef('serialNumber');
const isCompleted = await serialNumber?.getCompleteStatus();
if (!isCompleted) {
  ElMessage.warning('请等待附件上传完成');
  return;
}

//不在表单中,使用ref获取组件实例，然后调用getCompleteStatus获取上传完成结果
```

### 补充

在本组件中文件扩展名有兼容

```ts
/** 文件格式兼任 */
export const compatibleFormats: Record<string, string[]> = {
  doc: ['docx'],
  docx: ['doc'],
  ppt: ['pptx'],
  pptx: ['ppt'],
  xls: ['xlsx'],
  xlsx: ['xls'],
  zip: ['rar', '7z'],
  rar: ['zip', '7z'],
  '7z': ['zip', 'rar'],
};
```

#### 1.常见文件扩展名

| 文件扩展名 | 描述 |
| --- | --- |
| doc | 微软 Office Word 格式（Microsoft Word 97 - 2004 document） |
| xls | 微软 Office Excel 格式（Microsoft Excel 97 - 2004 Workbook |
| ppt | 微软 Office PowerPoint 格式（Microsoft PowerPoint 97 - 2003 演示文稿） |
| docx | 微软 Office Word 文档格式 |
| xlsx | 微软 Office Excel 文档格式 |
| pptx | 微软 Office PowerPoint 文稿格式 |
| zip | ZIP 压缩文件格式 |
| rar | RAR 压缩文件格式 |
| pdf | Portable Document Format |
| jpg | JPG图像格式 |
| png | PNG 图像格式 |
