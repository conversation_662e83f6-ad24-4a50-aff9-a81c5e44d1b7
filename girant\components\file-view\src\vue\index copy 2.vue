<script setup lang="ts">
/**
 * @file fileViewer.vue
 * @description 文件预览组件
 * <AUTHOR>
 * @date [2025-03-17]
 */
import { createApp, h, onMounted, ref } from 'vue';

import { RequestClient } from '@vben/request';

import { defaultRequestClient } from '@girant/utils';
// 引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx';
// 引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel';
// pdf组件
import VueOfficePdf from '@vue-office/pdf';
import { ElMessage } from 'element-plus';

// 引入相关图片
import { docx, pdf, rar, txt, unknow, xls, xlsx } from '../assets/index';

// 引入相关样式
import '@vue-office/docx/lib/index.css';
import '@vue-office/excel/lib/index.css';
// 设置文档地址
const props = defineProps({
  /** 文件id*/
  fileId: {
    default: '',
    type: String,
  },
  /** 文件类型*/
  fileType: {
    default: '',
    type: String,
  },
  /** height */
  height: {
    default: '30px',
    type: String,
  },
  /** 图标css */
  iconClass: {
    default: '',
    type: String,
  },
  /** 发送请求的客户端 */
  requestClient: {
    default: null,
    type: RequestClient,
  },
  /** 文件地址*/
  src: {
    default: null,
    type: String,
  },
  /** 不支持文字提示*/
  text: {
    default: '暂不支持该文件类型预览',
    type: String,
  },
  /** 模态框标题*/
  title: {
    default: '文件预览',
    type: String,
  },
  /** 请求路径*/
  urlHead: {
    default: '/file-manage/view/v1/file/download/',
    type: String,
  },
  /** width */
  width: {
    default: '30px',
    type: String,
  },
});
// 发送请求的客户端
const request = props.requestClient || defaultRequestClient;
// 要显示的文件地址
const fileUrl = ref();

/**
 * @function isFile
 * @description 判断是文件id还是src 并赋值
 */
const isFile = async () => {
  // 优先使用传入的src
  if (props.src) {
    fileUrl.value = props.src;
  }
  // 如果没有传入src，则使用fileId发送请求获取文件
  else {
    const res = await request.download(props.urlHead + props.fileId);
    fileUrl.value = URL.createObjectURL(res); // 创建一个 URL 对象
  }
  console.log(fileUrl.value);
};
// 解析后的txt内容
const resText = ref();
onMounted(async () => {
  if (props.fileType === 'txt') {
    const response = await fetch(fileUrl.value); // 获取文件
    const blob = await response.blob(); // 将响应转换为 Blob 对象
    resText.value = await blob.text(); // 将 Blob 对象转换为文本
  }
});
/** 图片 */
const imgPicker: Record<string, string> = {
  unknow,
  docx,
  pdf,
  txt,
  xlsx,
  xls,
  rar,
};
/** 支持预览的文件类型 */
const supportFileType = new Set(['docx', 'pdf', 'txt', 'xlsx']);
/** 文件预览点击 */
const handleFileClick = async () => {
  // 检查文件类型
  if (!supportFileType.has(props.fileType) || props.fileType === 'default') {
    ElMessage.warning('暂不支持该文件类型预览');
    return;
  }
  isFile().then(() => {
    openNewPage();
  });
};
defineExpose({});
/** 打开新的页面 */
const openNewPage = async () => {
  const newWindow: any = window.open('', '_blank');
  const app = createApp({
    render: () => {
      if (props.fileType === 'pdf') {
        return h(VueOfficePdf, { src: fileUrl.value });
      }
      if (props.fileType === 'docx') {
        return h(VueOfficeDocx, { src: fileUrl.value });
      }
      if (props.fileType === 'xlsx') {
        return h(VueOfficeExcel, { src: fileUrl.value });
      }
      if (props.fileType === 'txt') {
        return h('div', {}, resText.value);
      }
      return h('span', {}, props.text);
    },
  });
  app.mount(newWindow.document.body);
  newWindow.initData = {
    fileUrl: fileUrl.value,
    fileType: props.fileType,
    resText: resText.value,
    text: props.text,
  };
};
</script>

<template>
  <slot name="default">
    <img
      style="margin: auto; cursor: pointer"
      :style="{ height, width }"
      :class="iconClass"
      :src="imgPicker[fileType] || imgPicker.unknow"
      @click="handleFileClick"
    />
  </slot>
</template>
